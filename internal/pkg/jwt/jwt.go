package jwt

import (
	"fmt"
	"strconv"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// Claims JWT 声明结构
type Claims struct {
	UserID string `json:"user_id"`
	jwt.RegisteredClaims
}

// JWTManager JWT 管理器
type JWTManager struct {
	secretKey []byte
	expiry    time.Duration
}

// NewJWTManager 创建 JWT 管理器
func NewJWTManager(secretKey string, expiryStr string) (*JWTManager, error) {
	if secretKey == "" {
		return nil, fmt.Errorf("JWT secret key cannot be empty")
	}

	// 解析过期时间
	expiry, err := parseExpiry(expiryStr)
	if err != nil {
		return nil, fmt.Errorf("invalid JWT expiry: %w", err)
	}

	return &JWTManager{
		secretKey: []byte(secretKey),
		expiry:    expiry,
	}, nil
}

// GenerateToken 生成 JWT token
func (j *JWTManager) GenerateToken(userID string) (string, error) {
	if userID == "" {
		return "", fmt.Errorf("user ID cannot be empty")
	}

	now := time.Now()
	claims := &Claims{
		UserID: userID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(j.expiry)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "hostaudit",
			Subject:   userID,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString(j.secretKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, nil
}

// ValidateToken 验证 JWT token
func (j *JWTManager) ValidateToken(tokenString string) (*Claims, error) {
	if tokenString == "" {
		return nil, fmt.Errorf("token cannot be empty")
	}

	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return j.secretKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	claims, ok := token.Claims.(*Claims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("invalid token claims")
	}

	return claims, nil
}

// parseExpiry 解析过期时间字符串为 time.Duration
func parseExpiry(expiryStr string) (time.Duration, error) {
	if expiryStr == "" {
		return 7 * 24 * time.Hour, nil // 默认 7 天
	}

	// 尝试解析为秒数
	seconds, err := strconv.ParseInt(expiryStr, 10, 64)
	if err != nil {
		// 如果不是数字，尝试解析为 duration 字符串
		duration, err := time.ParseDuration(expiryStr)
		if err != nil {
			return 0, fmt.Errorf("invalid expiry format: %s", expiryStr)
		}
		return duration, nil
	}

	return time.Duration(seconds) * time.Second, nil
}
