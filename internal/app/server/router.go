package server

import (
	"net/http"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"

	"jiansec/hostaudit/internal/app/handlers"
	"jiansec/hostaudit/internal/config"
	"jiansec/hostaudit/internal/pkg/jwt"
	"jiansec/hostaudit/internal/storage"
)

// NewRouter 创建路由
func NewRouter(cfg *config.Config, store *storage.Storage, jwtManager *jwt.JWTManager) http.Handler {
	r := chi.NewRouter()

	// 基础中间件
	r.Use(middleware.RequestID)
	r.Use(middleware.RealIP)
	r.Use(MiddlewareLogger(cfg))
	r.Use(middleware.Recoverer)
	r.Use(middleware.Timeout(60 * time.Second))
	r.Use(middleware.NoCache) // 禁用缓存
	r.Use(MiddlewareCors(cfg))

	// 创建处理器
	h := handlers.New(cfg, store, jwtManager)

	// 健康检查
	r.Get("/health", h.Health)

	// OAuth 路由
	r.Get("/oauth/{provider}/login", h.OAuthLogin)
	r.Get("/oauth/{provider}/callback", h.OAuthCallback)

	// API 路由组
	r.Route("/api/v1", func(r chi.Router) {
		r.Get("/example", func(w http.ResponseWriter, r *http.Request) {
			w.Write([]byte("This is an example API endpoint."))
		})
		r.Get("/profile", h.UserProfile)
	})

	// 静态文件服务 TODO

	return r
}
