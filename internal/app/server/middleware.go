package server

import (
	"net/http"
	"runtime"

	"github.com/go-chi/chi/v5/middleware"

	"jiansec/hostaudit/internal/config"
	"jiansec/hostaudit/internal/pkg/logger"
)

// MiddlewareLogger 返回 Chi 中间件日志记录器
func MiddlewareLogger(cfg *config.Config) func(next http.Handler) http.Handler {
	// 判断是否需要彩色输出
	color := shouldUseColor(cfg)
	logger := logger.NewLogger(cfg)

	return middleware.RequestLogger(&middleware.DefaultLogFormatter{
		Logger:  logger,
		NoColor: !color,
	})
}

// shouldUseColor 判断是否应该使用彩色输出
func shouldUseColor(cfg *config.Config) bool {
	// Windows 系统通常不支持彩色输出
	if runtime.GOOS == "windows" {
		return false
	}

	// 只有输出到终端时才使用彩色
	return cfg.ServerLog == "stdout" || cfg.ServerLog == "stderr"
}

// MiddlewareCors 返回 Chi 中间件 CORS 处理器
func MiddlewareCors(cfg *config.Config) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// 开发环境下允许跨域请求
			if cfg.IsDevelopment() {
				w.Header().Set("Access-Control-Allow-Origin", "*")
				w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
				w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
				w.Header().Set("Access-Control-Allow-Credentials", "true")
			}
			// OPTIONS 请求预检
			if r.Method == "OPTIONS" {
				w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE")
				w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
				w.WriteHeader(http.StatusNoContent)
				return
			}
			next.ServeHTTP(w, r)
		})
	}
}
