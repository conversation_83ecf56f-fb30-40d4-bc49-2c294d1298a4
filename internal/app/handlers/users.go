package handlers

import (
	"jiansec/hostaudit/internal/pkg/utils"
	"net/http"
)

// UserProfile 获取用户信息
func (h *Handlers) UserProfile(w http.ResponseWriter, r *http.Request) {
	session, err := h.cookieStore.Get(r, h.SessionName)
	if err != nil {
		h.respondSystemError(w, "Failed to get session")
		return
	}

	userID := utils.GetSessionValue[string](session, "user_id")
	if userID == "" {
		h.respondSystemError(w, "Failed to get user ID from session")
		return
	}

	// 从存储获取用户信息
	user, err := h.storage.Users.GetByID(r.Context(), userID)
	if err != nil {
		h.respondSystemError(w, "Failed to get user profile")
		return
	}

	h.respondSuccess(w, user)
}
