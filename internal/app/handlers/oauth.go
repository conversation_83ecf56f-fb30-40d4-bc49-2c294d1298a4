package handlers

import (
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"net/url"

	"github.com/go-chi/chi/v5"
	"golang.org/x/oauth2"

	"jiansec/hostaudit/internal/pkg/utils"
	"jiansec/hostaudit/internal/users"
)

func (h *Handlers) OAuthLogin(w http.ResponseWriter, r *http.Request) {
	redirectURI := r.URL.Query().Get("redirect_uri")
	if redirectURI == "" {
		log.Println("OAuth Login Error: Missing redirect_uri parameter")
		http.Error(w, "Missing redirect_uri parameter", http.StatusBadRequest)
		return
	}

	providerName := chi.URLParam(r, "provider")
	provider, exists := h.oAuthManager.GetProvider(providerName)
	if !exists {
		log.Printf("OAuth '%s' Login Error: OAuth provider not found", providerName)
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}

	session, err := h.cookieStore.Get(r, h.SessionName)
	if err != nil {
		log.Printf("OAuth '%s' Login Error: %v", providerName, err)
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}

	oauthState := fmt.Sprintf("%d", rand.Int63())
	oauthVerifier := oauth2.GenerateVerifier()

	session.Values["oauth_provider"] = providerName
	session.Values["oauth_state"] = oauthState
	session.Values["oauth_verifier"] = oauthVerifier

	// 保存 session
	if err := session.Save(r, w); err != nil {
		log.Printf("OAuth '%s' Login Error: Failed to save session: %v", providerName, err)
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}
	// 生成 OAuth 授权 URL
	authURL := provider.GetAuthURL(oauthState, oauthVerifier)
	if authURL == "" {
		log.Printf("OAuth '%s' Login Error: Failed to get auth URL", providerName)
		http.Redirect(w, r, redirectURI, http.StatusTemporaryRedirect)
		return
	}

	http.Redirect(w, r, authURL, http.StatusFound)
}

func (h *Handlers) OAuthCallback(w http.ResponseWriter, r *http.Request) {
	redirectURI := r.URL.Query().Get("redirect_uri")
	if redirectURI == "" {
		log.Println("OAuth Callback Error: Missing redirect_uri parameter")
		http.Error(w, "Missing redirect_uri parameter", http.StatusBadRequest)
		return
	}

	redirectURL, err := url.Parse(redirectURI)
	if err != nil {
		log.Printf("OAuth Callback Error: Invalid redirect URI: %v", err)
		http.Error(w, "Invalid redirect URI", http.StatusBadRequest)
		return
	}

	providerName := chi.URLParam(r, "provider")
	provider, exists := h.oAuthManager.GetProvider(providerName)
	if !exists {
		log.Printf("OAuth '%s' Callback Error: OAuth provider not found", providerName)
		http.Error(w, "OAuth provider not found", http.StatusInternalServerError)
		return
	}

	session, err := h.cookieStore.Get(r, h.SessionName)
	if err != nil {
		log.Printf("OAuth '%s' Callback Error: Failed to get session: %v", providerName, err)
		http.Error(w, "Failed to get session", http.StatusInternalServerError)
		return
	}

	// 从 session 中获取 state
	state := r.URL.Query().Get("state")
	expectedState := utils.GetSessionValue[string](session, "oauth_state")
	if expectedState == "" {
		log.Printf("OAuth '%s' Callback Error: Missing expected state in session", providerName)
		http.Error(w, "Missing expected state in session", http.StatusInternalServerError)
		return
	}

	// 如果 state 不匹配，说明是 CSRF 攻击，拒绝处理
	if state != expectedState {
		log.Printf("OAuth '%s' Callback Error: Invalid oauth state, expected '%s', got '%s'\n", providerName, expectedState, state)
		http.Error(w, "Invalid oauth state", http.StatusForbidden)
		return
	}

	code := r.URL.Query().Get("code")
	if code == "" {
		log.Printf("OAuth '%s' Callback Error: Missing code in callback %s", providerName, r.URL.Query().Get("error"))
		http.Error(w, "Missing code in callback", http.StatusInternalServerError)
		return
	}

	// 从 session 中获取 oauth_verifier
	oauthVerifier := utils.GetSessionValue[string](session, "oauth_verifier")
	if oauthVerifier == "" {
		log.Printf("OAuth '%s' Callback Error: Missing oauth_verifier in session", providerName)
		http.Error(w, "Missing oauth_verifier in session", http.StatusInternalServerError)
		return
	}

	user, err := provider.HandleCallback(code, oauthVerifier)
	if err != nil {
		log.Printf("OAuth '%s' Callback Error: %v", providerName, err)
		http.Error(w, "Failed to handle callback", http.StatusInternalServerError)
		return
	}

	if err := user.Validate(); err != nil {
		log.Printf("OAuth '%s' Callback Error: Failed to validate user: %v", providerName, err)
		http.Error(w, "Failed to validate user", http.StatusInternalServerError)
		return
	}

	log.Printf("OAuth '%s' Callback Success: Get feishu userinfo success, [%s]", providerName, user.Username)

	// 清理 OAuth 相关的 session 值
	delete(session.Values, "oauth_provider")
	delete(session.Values, "oauth_state")
	delete(session.Values, "oauth_verifier")

	// 保存 session
	if err := session.Save(r, w); err != nil {
		log.Printf("OAuth '%s' Callback Error: Failed to save session: %v", providerName, err)
		http.Error(w, "Failed to save session", http.StatusInternalServerError)
		return
	}

	var registeredUser *users.User
	hasSuperAdmin, err := h.storage.Users.HasSuperAdmin(r.Context())
	if err != nil {
		log.Printf("OAuth '%s' Callback Error: Failed to check super admin: %v", providerName, err)
		http.Error(w, "Failed to check super admin", http.StatusInternalServerError)
		return
	}

	// 如果还没有超级管理员，创建一个
	if !hasSuperAdmin {
		log.Println("Creating super admin")

		registeredUser, err = h.storage.Users.CreateSuperAdmin(r.Context(), user)
		if err != nil {
			log.Printf("OAuth '%s' Callback Error: Failed to create super admin: %v", providerName, err)
			http.Error(w, "Failed to create super admin", http.StatusInternalServerError)
			return
		}

		if err := registeredUser.Validate(); err != nil {
			log.Printf("OAuth '%s' Callback Error: Failed to validate super admin: %v", providerName, err)
			http.Error(w, "Failed to validate super admin", http.StatusInternalServerError)
			return
		}

		log.Printf("Super admin [%s] created", registeredUser.Username)
	} else {
		registeredUser, err = h.storage.Users.RegisterOrUpdate(r.Context(), user)
		if err != nil {
			log.Printf("OAuth '%s' Callback Error: Failed to register/update user: %v", providerName, err)
			http.Error(w, "Failed to register/update user", http.StatusInternalServerError)
			return
		}

		if err := registeredUser.Validate(); err != nil {
			log.Printf("OAuth '%s' Callback Error: Failed to validate user: %v", providerName, err)
			http.Error(w, "Failed to validate user", http.StatusInternalServerError)
			return
		}

		log.Printf("User [%s] registered/updated", registeredUser.Username)
	}

	// 生成 JWT Token
	token, err := h.jwtManager.GenerateToken(registeredUser.ID)
	if err != nil {
		log.Printf("OAuth '%s' Callback Error: Failed to generate JWT token: %v", providerName, err)
		http.Error(w, "Failed to generate JWT token", http.StatusInternalServerError)
		return
	}

	// 将 JWT Token 添加到重定向 URL 的查询参数中
	query := redirectURL.Query()
	query.Set("token", token)
	redirectURL.RawQuery = query.Encode()

	// 将用户ID存储到 session 中
	session.Values["user_id"] = registeredUser.ID
	if err := session.Save(r, w); err != nil {
		log.Printf("OAuth '%s' Callback Error: Failed to save session: %v", providerName, err)
		http.Error(w, "Failed to save session", http.StatusInternalServerError)
		return
	}

	log.Printf("OAuth '%s' Callback Success: User [%s] authenticated with JWT token",
		providerName, registeredUser.Username)

	http.Redirect(w, r, redirectURL.String(), http.StatusFound)
}
