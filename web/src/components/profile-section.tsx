import React, { useState } from 'react'
import Avatar from '@mui/material/Avatar'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import ClickAwayListener from '@mui/material/ClickAwayListener'
import Divider from '@mui/material/Divider'
import Fade from '@mui/material/Fade'
import List from '@mui/material/List'
import ListItem from '@mui/material/ListItem'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import Popper from '@mui/material/Popper'
import Stack from '@mui/material/Stack'
import { useTheme } from '@mui/material/styles'
import Switch from '@mui/material/Switch'
import { IconLogout, IconSettings, IconSunMoon } from '@tabler/icons-react'

import { MainCard } from '@/components/design-systems/main-card'
import { Profile, type ProfileProps } from '@/components/design-systems/profile'
import { ThemeDirection, ThemeMode } from '@/config'
import { useAuth } from '@/contexts/auth'
import { useConfig } from '@/contexts/config'
import { AuthRole } from '@/hooks/api/user'
import { useCurrentUser } from '@/hooks/use-current-user'
import { AvatarSize } from '@/theme/enum/avatar'

/***************************  HEADER - PROFILE DATA  ***************************/

const RoleTitles = {
  [AuthRole.SUPER_ADMIN]: 'Super Admin',
  [AuthRole.ADMIN]: 'Admin',
  [AuthRole.USER]: 'User'
}

/***************************  HEADER - PROFILE  ***************************/

export const ProfileSection = () => {
  const theme = useTheme()
  const { logout } = useAuth()
  const { setThemeMode } = useConfig()
  const { user } = useCurrentUser()

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)

  const open = Boolean(anchorEl)
  const id = open ? 'profile-action-popper' : undefined
  const buttonStyle = { borderRadius: 2, p: 1 }

  const profileData: ProfileProps = React.useMemo(() => {
    return {
      avatar: { src: user.avatar_url, size: AvatarSize.SM },
      title: user.nickname || user.username,
      caption: RoleTitles[user.role] || 'Unknown Role'
    }
  }, [user])

  const handleActionClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(anchorEl ? null : event.currentTarget)
  }

  const logoutAccount = () => {
    setAnchorEl(null)
    logout()
  }

  return (
    <>
      <Box
        onClick={handleActionClick}
        sx={{ cursor: 'pointer', WebkitTapHighlightColor: 'transparent' }}
      >
        <Box sx={{ display: { xs: 'none', sm: 'flex' } }}>
          <Profile {...profileData} />
        </Box>
        <Box sx={{ display: { xs: 'block', sm: 'none' } }}>
          <Avatar {...profileData.avatar} alt={profileData.title} />
        </Box>
      </Box>
      <Popper
        placement='bottom-end'
        id={id}
        open={open}
        anchorEl={anchorEl}
        transition
        popperOptions={{
          modifiers: [
            {
              name: 'offset',
              options: { offset: [theme.direction === ThemeDirection.RTL ? -8 : 8, 8] }
            }
          ]
        }}
      >
        {({ TransitionProps }) => (
          <Fade in={open} {...TransitionProps}>
            <MainCard
              sx={{
                borderRadius: 2,
                boxShadow: theme.customShadows.tooltip,
                minWidth: 220,
                p: 0.5
              }}
            >
              <ClickAwayListener onClickAway={() => setAnchorEl(null)}>
                <Stack sx={{ px: 0.5, py: 0.75 }}>
                  <Profile
                    {...profileData}
                    sx={{
                      'flexDirection': 'column',
                      'justifyContent': 'center',
                      'textAlign': 'center',
                      'width': 1,
                      '& .MuiAvatar-root': { width: 48, height: 48 }
                    }}
                  />
                  <Divider sx={{ my: 1 }} />
                  <List disablePadding>
                    <ListItem
                      secondaryAction={
                        <Switch
                          size='small'
                          checked={theme.palette.mode === ThemeMode.DARK}
                          onChange={() =>
                            setThemeMode(
                              theme.palette.mode === ThemeMode.DARK
                                ? ThemeMode.LIGHT
                                : ThemeMode.DARK
                            )
                          }
                        />
                      }
                      sx={{
                        'py': 0.5,
                        'pl': 1,
                        '& .MuiListItemSecondaryAction-root': { right: 8 }
                      }}
                    >
                      <ListItemIcon>
                        <IconSunMoon size={16} />
                      </ListItemIcon>
                      <ListItemText primary='Dark Theme' />
                    </ListItem>

                    <ListItemButton href='#' sx={{ ...buttonStyle, my: 0.5 }}>
                      <ListItemIcon>
                        <IconSettings size={16} />
                      </ListItemIcon>
                      <ListItemText primary='Settings' />
                    </ListItemButton>
                    <ListItem disablePadding>
                      <Button
                        fullWidth
                        variant='outlined'
                        color='secondary'
                        size='small'
                        endIcon={<IconLogout size={16} />}
                        onClick={logoutAccount}
                      >
                        Logout
                      </Button>
                    </ListItem>
                  </List>
                </Stack>
              </ClickAwayListener>
            </MainCard>
          </Fade>
        )}
      </Popper>
    </>
  )
}
