import { format } from 'date-fns'
import { z } from 'zod'

import { API_CHANGE_PASSWORD, API_PROFILE } from '@/constants/api-urls'
import { DATE_TIME_FORMAT } from '@/constants/date-format'
import {
  apiRequest,
  ApiRequestDataError,
  type ApiResponse,
  ApiResponseDataError,
  createApiResponseSchema
} from '@/lib/api-request'

// API API_CHANGE_PASSWORD

// Schema
export const ChangePasswordRequestDataSchema = z.object({
  current_password: z.string().min(6, '请输入当前密码'),
  new_password: z.string().min(6, '请输入新密码')
})

// Type
export type ChangePasswordRequestData = z.infer<typeof ChangePasswordRequestDataSchema>

// Request
export const changePasswordRequest = async (data: ChangePasswordRequestData) => {
  // Validate Request Data
  const parsedData = ChangePasswordRequestDataSchema.safeParse(data)
  if (!parsedData.success) {
    throw new ApiRequestDataError(parsedData.error)
  }
  const apiResponse = await apiRequest.post<ApiResponse<never>>(API_CHANGE_PASSWORD, data)

  return apiResponse.data
}

// API API_PROFILE

export enum AuthRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  USER = 'user'
}

// Schema
export const UserSchema = z.object({
  id: z.string(),
  username: z.string(),
  nickname: z.string(),
  avatar_url: z.string(),
  role: z.nativeEnum(AuthRole),
  raw_data: z.record(z.any()),
  created_at: z.string().transform(date => format(date, DATE_TIME_FORMAT)),
  updated_at: z.string().transform(date => format(date, DATE_TIME_FORMAT))
})

export const UserProfileResponseSchema = createApiResponseSchema(UserSchema)

// Type
export type UserProfileResponse = z.infer<typeof UserProfileResponseSchema>
export type User = z.infer<typeof UserSchema>

// Request
export const userProfileRequest = async () => {
  const apiResponse = await apiRequest.get<UserProfileResponse>(API_PROFILE)
  // Validate Response Data
  const parsedResponse = UserProfileResponseSchema.safeParse(apiResponse)
  if (!parsedResponse.success) {
    console.error('UserProfileResponseSchema', parsedResponse.error)
    throw new ApiResponseDataError(parsedResponse.error)
  }
  return parsedResponse.data
}
